package com.layaa.roomlogic.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.layaa.language.LanguageController
import com.layaa.libui.dialog.BaseBottomSheetDialogFragment
import com.layaa.libui.dialog.CommonTipDialog
import com.layaa.roomlogic.R
import com.layaa.roomlogic.databinding.FragmentMicModeBinding
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import com.layaa.roomlogic.voiceroom.adapter.MicModeAdapter
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity.Companion.MODE_EIGHT
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity.Companion.MODE_FIFTEEN
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity.Companion.MODE_FIVE
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity.Companion.MODE_NINE
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity.Companion.MODE_TWELVE
import com.layaa.roomlogic.voiceroom.entity.MicModeEntity.Companion.MODE_TWO
import com.layaa.libutils.UIUtils
import com.layaa.widget.GridLayoutManagerWrapper
import org.greenrobot.eventbus.EventBus

/**
 *<AUTHOR>
 *@date 2024/4/27
 *@des
 **/
class MicModeFragment :
    BaseBottomSheetDialogFragment<RoomInfoViewModel, FragmentMicModeBinding>() {

    companion object {
        private const val FROM_OPEN = "from_open"

        @JvmStatic
        fun start(fragmentManager: FragmentManager) {
            val fragment = MicModeFragment()
            fragment.showNow(fragmentManager, MicModeFragment::class.java.simpleName)
        }

        @JvmStatic
        fun startFromOpen(fragmentManager: FragmentManager) {
            val fragment = MicModeFragment()
            fragment.arguments = Bundle().apply {
                putBoolean(FROM_OPEN, true)
            }
            fragment.showNow(fragmentManager, MicModeFragment::class.java.simpleName)
        }
    }

    private var isFromOpen = false

    private var confirmDialog: CommonTipDialog? = null

    private val adapter by lazy {
        MicModeAdapter(requireContext()).apply {
            callback = {
                if (isFromOpen) {
                    EventBus.getDefault().post(MicModeSelectEvent(this))
                    dismiss()
                } else {
                    showConfirmDialog(this)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        isFromOpen = arguments?.getBoolean(FROM_OPEN, false) ?: false
    }

    override fun initBinding(
        inflater: LayoutInflater,
        parent: ViewGroup?,
        attachToParent: Boolean
    ): FragmentMicModeBinding {
        return FragmentMicModeBinding.inflate(inflater, parent, attachToParent)
    }

    override fun getViewModel(): Class<RoomInfoViewModel> {
        return RoomInfoViewModel::class.java
    }

    override fun initAction() {

        binding.micModeList.layoutManager = GridLayoutManagerWrapper(context, 3)
        binding.micModeList.itemAnimator = null
        binding.micModeList.adapter = adapter

        val list = arrayListOf<MicModeEntity>()
        list.add(MicModeEntity(MODE_TWO, R.drawable.icon_mic_two, UIUtils.getString(R.string.mic_mode_two)))
        list.add(MicModeEntity(MODE_FIVE, R.drawable.icon_mic_five, UIUtils.getString(R.string.mic_mode_five)))
        list.add(MicModeEntity(MODE_EIGHT,R.drawable.icon_mic_eight, UIUtils.getString(R.string.mic_mode_eight)))
        list.add(MicModeEntity(MODE_NINE, R.drawable.icon_mic_nine, UIUtils.getString(R.string.mic_mode_nine)))
        list.add(MicModeEntity(MODE_TWELVE, R.drawable.icon_mic_twelve, UIUtils.getString(R.string.mic_mode_twelve)))
        list.add(MicModeEntity(MODE_FIFTEEN, R.drawable.icon_mic_fiften, UIUtils.getString(R.string.mic_mode_fifteen)))
        adapter.refreshList(list)

        viewModel?.roomConfigData?.observe(this) {

        }

        viewModel?.updateRoomConfig()
    }

    override fun getHeight(): Int {
        return UIUtils.getPixels(321F)
    }

    private fun showConfirmDialog(mode: MicModeEntity) {
        if (confirmDialog == null) {
            confirmDialog = CommonTipDialog(requireContext())
            confirmDialog?.setContent(LanguageController.getInstance().getString(R.string.room_tips_switch_mic_mode))
            confirmDialog?.confirmCallback = {
                (confirmDialog?.tag as? MicModeEntity)?.let {
                    viewModel?.updateSeatsCount(it.id)
                    dismiss()
                }
            }
        }
        confirmDialog?.show(mode)
    }

    override fun onDestroy() {
        super.onDestroy()
        confirmDialog?.dismiss()
    }

    inner class MicModeSelectEvent(val mode: MicModeEntity)
}