package com.layaa.roomlogic.util

import androidx.lifecycle.MutableLiveData
import com.layaa.accountapi.login.LoginRouter
import com.layaa.im.gson.GsonUtils
import com.layaa.libnet.util.handle
import com.layaa.libutils.kv.KVDelegate
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.roomlogic.api.RoomRepository
import com.layaa.roomlogic.voiceroom.entity.RoomConfig
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Created by <PERSON> on 25/09/02_周二
 */
object RoomConfigManager {

    private const val KV_ROOM_CONFIG = "room_config"

    val roomConfigData = MutableLiveData<RoomConfig>()

    private val coroutineScope =
        CoroutineScope(ThreadPool.instance.asDispatcher() + CoroutineName("RoomConfigManager"))

    fun updateRoomConfig(roomId: String? = null) = coroutineScope.launch(Dispatchers.Main) {
        getRoomConfig(roomId)
    }

    private suspend fun getRoomConfig(roomId: String?): RoomConfig {
        val configKey = if (roomId == null) {
            LoginRouter.getUserId() + KV_ROOM_CONFIG
        } else {
            roomId + KV_ROOM_CONFIG
        }
        return try {
            RoomRepository.fetchRoomConfig(roomId).also {
                KVDelegate.getInstance().save(configKey, GsonUtils.toJson(this))
            }
        } catch (e: Exception) {
            e.handle()
            LogUtils.e(e)
            GsonUtils.fromJson(
                KVDelegate.getInstance().getString(configKey, ""),
                RoomConfig::class.java
            ) ?: RoomConfig()
        }
    }

    fun isShowEmojiInChat(): Boolean {
        return roomConfigData.value?.isShowEmojiInChat ?: true
    }

}