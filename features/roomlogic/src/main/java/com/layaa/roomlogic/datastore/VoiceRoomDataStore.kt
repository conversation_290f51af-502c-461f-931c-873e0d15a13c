package com.layaa.roomlogic.datastore

import android.text.TextUtils
import android.view.KeyEvent
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import com.google.gson.reflect.TypeToken
import com.layaa.accountapi.login.LoginRouter
import com.layaa.chatapi.ImEvent
import com.layaa.im.bean.MessageTypedEnvelope
import com.layaa.libnet.util.Failure
import com.layaa.libnet.util.Success
import com.layaa.libui.utils.TypefaceHelper
import com.layaa.libutils.GsonUtils
import com.layaa.libutils.module_thread.task.ThreadPool
import com.layaa.roomapi.RoomConst
import com.layaa.roomapi.RoomObserver
import com.layaa.roomapi.entity.RoomThemeInfo
import com.layaa.roomapi.entity.RoomThemePropInfo
import com.layaa.roomlogic.R
import com.layaa.roomlogic.entity.RoomProfileEntity
import com.layaa.roomlogic.entity.RoomSeatInfo
import com.layaa.roomlogic.entity.RoomTopListEntity
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.entity.SpeakConfigDto
import com.layaa.roomlogic.entity.UserPropType
import com.layaa.roomlogic.entity.VoiceRoomEntity
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.cell.VoiceRoomGroup
import com.layaa.roomlogic.module.event.CloseSeatAnimEvent
import com.layaa.roomlogic.module.event.PlaySeatVoiceAnimEvent
import com.layaa.roomlogic.module.event.StopSeatVoiceAnimEvent
import com.layaa.roomlogic.module.event.UpdateSeatEvent
import com.layaa.roomlogic.module.event.UserEnterRoomEvent
import com.layaa.roomlogic.module.helper.MediaHelper
import com.layaa.roomlogic.module.helper.RoomEventHelper
import com.layaa.roomlogic.module.helper.RoomImHelper
import com.layaa.roomlogic.module.pk.PKEndEvent
import com.layaa.roomlogic.module.pk.PKInfo
import com.layaa.roomlogic.module.pk.PKOpenEvent
import com.layaa.roomlogic.module.pk.PKResult
import com.layaa.roomlogic.module.pk.PKStatus
import com.layaa.roomlogic.module.pk.PKValue
import com.layaa.roomlogic.module.pk.PKViewModel
import com.layaa.roomlogic.module.score.ScoreAction
import com.layaa.roomlogic.module.score.ScoreBoard
import com.layaa.roomlogic.module.score.ScoreDuration
import com.layaa.roomlogic.module.score.ScoreStatusUpdate
import com.layaa.roomlogic.module.score.ScoreUser
import com.layaa.roomlogic.module.score.ScoreValueUpdate
import com.layaa.roomlogic.module.score.ScoreViewModel
import com.layaa.roomlogic.notify.RoomNotificationCenter
import com.layaa.roomlogic.rocket.bean.RocketData
import com.layaa.roomlogic.util.RoomChatSpanBuilder
import com.layaa.roomlogic.viewmodel.RoomBackgroundViewModel
import com.layaa.roomlogic.viewmodel.RoomInfoViewModel
import org.greenrobot.eventbus.Subscribe
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicInteger

/**
 *<AUTHOR>
 *@date 2024/5/13
 *@des
 **/
class VoiceRoomDataStore(private val activity: AppCompatActivity, parentLayout: ViewGroup) :
    BaseRoomDataStore(activity) {

    private val viewModel: RoomInfoViewModel =
        ViewModelProvider(activity)[RoomInfoViewModel::class.java]
    private val scoreViewModel: ScoreViewModel =
        ViewModelProvider(activity)[ScoreViewModel::class.java]
    private val pkViewModel: PKViewModel =
        ViewModelProvider(activity)[PKViewModel::class.java]
    private val backgroundViewModel: RoomBackgroundViewModel =
        ViewModelProvider(activity)[RoomBackgroundViewModel::class.java]
    private val voiceRoomGroup: VoiceRoomGroup = VoiceRoomGroup(parentLayout)
    private val errorMicCount = AtomicInteger(0)

    init {
        viewModel.roomMode = RoomMode.MODE_VOICE
        voiceRoomGroup.onCreate()
        viewModel.enterRoomData.observe(activity) {
            if (it is Success) {
                enterRoomSuccess(it.value)
            } else if (it is Failure) {
                enterRoomFailed(it.t)
            }
        }
        viewModel.roomQueryData.observe(activity) {
            if (it is Success) {
                queryRoomProfileSuccess(it.value)
            } else {
                queryRoomProfileFailed()
            }
        }
        viewModel.seatUserData.observe(activity) {
            selfSeatId = it[LoginRouter.getUserId()]?.seatInfo?.seatId ?: -1
        }
    }

    override fun enterRoom(roomId: String?, password: String?) {
        roomId?.let {
            RoomImHelper.instance.enterRoom(it, currentRoomMode)
            registerIMEvent()
        }
        viewModel.enterRoom(roomId, password)
        viewModel.updateRoomConfig(roomId)
    }

    override fun queryRoomProfile(roomId: String) {
        viewModel.queryRoomInfo(roomId)
    }

    override fun asyncRoomInfo(roomId: String?) {
        viewModel.asyncRoomInfo(roomId)
    }

    override fun updateRoomInfo(originStr: String?) {
        val roomInfo = GsonUtils.fromJson(originStr, VoiceRoomEntity::class.java) ?: return
        ThreadPool.runOnUiThread {
            changeRoomModel(currentRoomMode, roomInfo.roomProfileDTO?.roomType)
        }
        viewModel.roomRoleData.postValue(roomInfo.role)
        viewModel.roomLockData.postValue(roomInfo.roomProfileDTO?.isLocked())

        roomInfo.roomProfileDTO?.let {
            var speakConfigDto = it.speakConfigDto
            if (speakConfigDto == null) {
                speakConfigDto = SpeakConfigDto()
            }
            viewModel.speakConfigData.postValue(speakConfigDto)

            if (isPendingShowNoticeMsg) {
                isPendingShowNoticeMsg = false
                RoomImHelper.instance.addTextToMessageList(
                    RoomChatSpanBuilder.with(R.string.tips_enter_room)
                        .addText(activity.getString(R.string.app_name))
                        .baseColor(ContextCompat.getColor(activity, R.color.app_color))
                        .baseTypeface(TypefaceHelper.getMedium())
                        .build(),
                    ImEvent.CustomRoomMsg.TYPE_NORMAL_TIPS,
                )

                checkNoticeChange(it)
            }

            viewModel.roomProfileData.postValue(it.apply {
                ownerInfo = roomInfo.ownerInfo
            })
        }

        val scoreBoard = roomInfo.scoreboard
        roomInfo.seatPlayerInfoDTOList?.forEach { seatInfo ->
            if (!seatInfo.uid.isNullOrBlank()) {
                //座位上有用户
                fillScoreUserData(seatInfo, scoreBoard?.dataList)
                roomInfo.seatUserCommonDTOMap?.get(seatInfo.uid)?.seatInfo = seatInfo
            }
        }

        viewModel.seatUserData.postValue(roomInfo.seatUserCommonDTOMap)
        roomInfo.seatPlayerInfoDTOList?.let {
            //进房，麦位数用列表size
            viewModel.seatCountData.postValue(it.size)
            viewModel.seatData.postValue(it)
            SeatDataStore.instance.updateSelfSeatInfo(it)
        }
        viewModel.restrictedData.postValue(roomInfo.restrictedInfo)
        viewModel.relationStatusData.postValue(roomInfo.relationStatus)
        viewModel.roomUserCountData.postValue(roomInfo.onlineUserCount)
        roomInfo.imTopOnlineListUpdateInfo.let {
            if (lastTopUpdateTime <= it.sendTime) {
                viewModel.topListData.postValue(it)
                lastTopUpdateTime = it.sendTime
            }
        }
        viewModel.giftValueAmount.postValue(roomInfo.giftValueAmount)
        if (scoreBoard != null) {
            scoreViewModel.scoreStatusData.postValue(createScoreStatusUpdate(scoreBoard))
            createScoreValueUpdate(scoreBoard)?.let {
                scoreViewModel.scoreValueData.postValue(it)
            }
        }

        val pk = roomInfo.pk
        pkViewModel.pkInfoData.postValue(pk)
        if (pk != null && pk.pkStatus == PKStatus.IN_PROGRESS.value) {
            pkViewModel.pkValueData.postValue(pk)
        }

        updateRoomTheme(roomInfo.themeData)
    }

    override fun updateRoomTheme(data: Map<String, RoomThemePropInfo>?) {
        val themeInfo = RoomThemeInfo()
        data?.get(UserPropType.ROOM_THEME.code)?.let {
            themeInfo.themePropId = it.propId
            themeInfo.seatHeadwear = it.frame
            themeInfo.backgroundUrl = it.resourceContent
        }
        data?.get(UserPropType.ROOM_BACKGROUND.code)?.let {
            themeInfo.backgroundPropId = it.propId
            themeInfo.backgroundUrl = it.resourceContent
        }
        backgroundViewModel.roomThemeData.postValue(themeInfo)
    }

    private fun createScoreStatusUpdate(scoreBoard: ScoreBoard?) =
        ScoreStatusUpdate(
            if (scoreBoard?.activated == true) {
                if (scoreBoard.isOpen) {
                    ScoreAction.START.value
                } else {
                    ScoreAction.END.value
                }
            } else {
                ScoreAction.OFF.value
            }
        ).apply {
            boardId = scoreBoard?.boardId
            duration = scoreBoard?.duration ?: ScoreDuration.NO_LIMIT.minute
            startTime = scoreBoard?.startTime ?: 0
        }

    private fun createScoreValueUpdate(scoreBoard: ScoreBoard): ScoreValueUpdate? =
        if (scoreBoard.activated && scoreBoard.isOpen) {
            ScoreValueUpdate(scoreBoard.boardId, scoreBoard.dataList)
        } else {
            null
        }

    override fun userEnterRoom(userEntity: RoomUserEntity?) {
        userEntity?.let {
//            if (TextUtils.equals(it.uid, LoginRouter.getUserId())) {
//                return
//            }
//            RoomEventHelper.postEvent(UserEnterRoomEvent(it))
//            RoomEventHelper.postEvent(ReceiveGiftEvent().vehicle(it.vehicle))

            RoomEventHelper.postEvent(UserEnterRoomEvent(it))

        }
    }

    override fun receiveRoomEvent(message: MessageTypedEnvelope): Boolean {
        if (message !is MessageTypedEnvelope.SystemNoticeMessageEnvelope) {
            return false
        }
        val eventId = message.content.content.eventId
        val jsonData = JSONObject(message.content.content.data ?: emptyMap<String, String>())
        return when (eventId) {
            ImEvent.CustomRoomMsg.TYPE_SYNC_MIC -> {
                val type = object : TypeToken<List<RoomSeatInfo>>() {
                }.type
                val seatInfoDTOList = GsonUtils.fromJson<List<RoomSeatInfo>>(
                    jsonData.optJSONArray("seatInfo")?.toString(), type
                )

                val userType = object : TypeToken<HashMap<String, RoomUserEntity>>() {
                }.type
                val userInfo = GsonUtils.fromJson<HashMap<String, RoomUserEntity>>(
                    jsonData.optJSONObject("userInfo")?.toString(), userType
                )

                viewModel.seatData.value?.forEachIndexed { index, roomSeatInfo ->
                    if (!roomSeatInfo.uid.isNullOrBlank() && ((seatInfoDTOList?.size
                            ?: 0) > index)
                    ) {
                        val newSeatInfo = seatInfoDTOList?.get(index)
                        if (!TextUtils.equals(roomSeatInfo.uid, newSeatInfo?.uid)) {
                            //麦位人员变动 之前麦位人下麦
                            val uid = roomSeatInfo.uid
                            RoomEventHelper.postEvent(CloseSeatAnimEvent(uid))
                            RoomEventHelper.postEvent(UpdateSeatEvent(roomSeatInfo))
                            RoomEventHelper.postEvent(StopSeatVoiceAnimEvent(roomSeatInfo.seatId))
                            /* if (TextUtils.equals(uid, LoginRouter.getUserId())) {
                                 MediaHelper.INSTANCE.updateUserMicStatus(roomSeatInfo)
                                 RoomNotificationCenter.instances.sendNotification(RoomObserver.MIC_CHANGE)
                                 MusicPlayHelper.instance.stopMusic(true)
                             }*/
                        }
                    }
                }

                val scoreUserList = scoreViewModel.scoreValueData.value?.dataList
                seatInfoDTOList?.forEach { seatInfo ->
                    if (!seatInfo.uid.isNullOrBlank()) {
                        fillScoreUserData(seatInfo, scoreUserList)
                        userInfo?.get(seatInfo.uid)?.seatInfo = seatInfo
                    }
                }

                viewModel.seatData.postValue(seatInfoDTOList)
                viewModel.seatUserData.postValue(userInfo)
                RoomNotificationCenter.instances.sendNotification(RoomObserver.MIC_CHANGE)
                false
            }

            ImEvent.CustomRoomMsg.TYPE_MIC_MANAGER -> {
                val seatId = jsonData.optInt("seatId", -1)
                if (seatId < 0) {
                    return false
                }
                // 1 静音 2 解除静音  3 锁定 4 解除锁定
                val manageType = jsonData.optInt("manageType", -1)

                viewModel.seatData.value?.let {
                    if (it.size > seatId) {
                        val seat = it[seatId]
                        when (manageType) {
                            1 -> {
                                seat.muteStatus = 1
                                RoomEventHelper.postEvent(StopSeatVoiceAnimEvent(seatId))
                            }

                            2 -> {
                                seat.muteStatus = 0
                            }

                            3 -> {
                                seat.lockStatus = 1
                                RoomEventHelper.postEvent(StopSeatVoiceAnimEvent(seatId))
                            }

                            4 -> {
                                seat.lockStatus = 0
                            }
                        }
                        RoomEventHelper.postEvent(UpdateSeatEvent(seat))
                    }
                }
                false
            }

            ImEvent.CustomRoomMsg.TYPE_MIC_COUNT_CHANGE -> {
                jsonData.optInt("slotCount").let {
                    viewModel.seatCountData.postValue(it)
                }
                false
            }

            ImEvent.CustomRoomMsg.TYPE_ROOM_RANK_VALUE_CHANGE -> {
                jsonData.optLong("totalGiftValue").let {
                    viewModel.giftValueAmount.postValue(it)
                }
                false
            }

            ImEvent.CustomRoomMsg.TYPE_ROOM_CHANGE_INFO -> {
                jsonData.optJSONObject("roomProfileDTO")?.let {

                    val roomProfile =
                        GsonUtils.fromJson(it.toString(), RoomProfileEntity::class.java)
                            ?: return@let

                    checkNoticeChange(roomProfile)
                    viewModel.roomProfileData.postValue(roomProfile.apply {
                        if (ownerInfo == null) {//避免丢失房主信息
                            ownerInfo = viewModel.roomProfileData.value?.ownerInfo
                        }
                    })

                    roomProfile.speakConfigDto?.let { new ->
                        val old = viewModel.speakConfigData.value
                        if (new.level == null) {
                            new.level = old?.level
                        }
                        if (new.userRoleLimit == null) {
                            new.userRoleLimit = old?.userRoleLimit
                        }
                        if (new.enable == null) {
                            new.enable = old?.enable
                        }
                        viewModel.speakConfigData.postValue(new)
                    }
                }
                false
            }

            ImEvent.CustomRoomMsg.TYPE_ENTER_ROOM -> {
                val info = GsonUtils.fromJson(
                    jsonData.optJSONObject("enterRoomInfo")?.optJSONObject("userInfoDto")
                        ?.toString(),
                    RoomUserEntity::class.java
                )
                userEnterRoom(info)
                false
            }

            ImEvent.CustomRoomMsg.ONLINE_COUNT_CHANGE -> {
                jsonData.optInt("data").let {
                    viewModel.roomUserCountData.postValue(it)
                }
                false
            }

            ImEvent.CustomRoomMsg.TYPE_ROOM_SCORE_STATUS_UPDATE -> {
                val update = GsonUtils.fromJson(jsonData.toString(), ScoreStatusUpdate::class.java)
                scoreViewModel.scoreStatusData.postValue(update ?: return false)
                false
            }

            ImEvent.CustomRoomMsg.TYPE_ROOM_SCORE_VALUE_UPDATE -> {
                val update = GsonUtils.fromJson(jsonData.toString(), ScoreValueUpdate::class.java)
                scoreViewModel.scoreValueData.postValue(update ?: return false)
                false
            }
            ImEvent.CustomRoomMsg.ROOM_ROCKET_STATUS_CHANGE -> {
                val rocketData = GsonUtils.fromJson(jsonData.toString(), RocketData::class.java)
                rocketData?.let {
                    viewModel.setRoomRocketStatus(it)
                }
                false
            }

            ImEvent.CustomRoomMsg.PK_OPEN -> {
                val pkInfo = GsonUtils.fromJson(jsonData.toString(), PKInfo::class.java)?.copy(
                    PKStatus.PREPARE.value
                )
                pkViewModel.pkInfoData.postValue(pkInfo ?: return false)
                val originPKInfo = pkViewModel.pkInfoData.value
                if (originPKInfo?.pkStatus == null || originPKInfo.pkStatus == PKStatus.NOT_OPEN.value) {
                    RoomEventHelper.postEvent(PKOpenEvent())
                }
                false
            }

            ImEvent.CustomRoomMsg.PK_START -> {
                val pkInfo = GsonUtils.fromJson(jsonData.optString("pkData"), PKInfo::class.java)
                pkViewModel.pkInfoData.postValue(pkInfo ?: return false)
                pkViewModel.pkValueData.postValue(pkInfo)
                false
            }

            ImEvent.CustomRoomMsg.PK_VALUE_CHANGE -> {
                val pkValue =
                    GsonUtils.fromJson(jsonData.optString("scoreData"), PKValue::class.java)
                pkViewModel.pkValueData.postValue(pkValue ?: return false)
                false
            }

            ImEvent.CustomRoomMsg.PK_END -> {
                val pkResult = GsonUtils.fromJson(jsonData.optString("data"), PKResult::class.java)
                RoomEventHelper.postEvent(PKEndEvent(pkResult ?: return false))
                val originPKInfo = pkViewModel.pkInfoData.value ?: return false
                pkViewModel.pkInfoData.postValue(originPKInfo.copy(PKStatus.PREPARE.value))
                false
            }

            ImEvent.CustomRoomMsg.PK_CLOSE -> {
                pkViewModel.pkInfoData.postValue(PKInfo(pkStatus = PKStatus.NOT_OPEN.value))
                false
            }

            else -> {
                false
            }
        }
    }

    private fun checkNoticeChange(entity: RoomProfileEntity) {
        if (entity.notice != viewModel.roomProfileData.value?.notice) {
            val notice = if (TextUtils.isEmpty(entity.notice)) {
                activity.getString(
                    R.string.room_notice_hint_normal,
                    activity.getString(R.string.app_name)
                )
            } else {
                entity.notice
            }
            RoomImHelper.instance.addTextToMessageList(
                notice,
                ImEvent.CustomRoomMsg.TYPE_NOTICE,
            )
        }
    }

    private fun fillScoreUserData(
        seatInfo: RoomSeatInfo,
        scoreUserList: List<ScoreUser>?
    ) {
        val defaultScore =
            if (scoreViewModel.scoreStatusData.value?.action == ScoreAction.OFF.value) {
                RoomSeatInfo.SCORE_VALUE_INVALID
            } else {
                0
            }
        seatInfo.scoreValue =
            scoreUserList?.getOrNull(scoreUserList.indexOf(ScoreUser().apply {
                uid = seatInfo.uid
            }))?.score ?: defaultScore
    }

    override fun dispatchKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        return voiceRoomGroup.dispatchKeyDown(keyCode, event)
    }

    override fun receiveEvent(event: Int, data: JSONObject?): Boolean {
        return false
    }

    override fun onRefreshRoomToken(roomId: String) {
        viewModel.refreshRoomToken(roomId)
    }

    override fun quitRoom(roomId: String?, reason: String?) {
        /* if (viewModel.isRoomOwn()) {

         } else {
             viewModel.quitRoom(roomId)
         }*/

    }

    override fun changeRoomModel(roomMode: Int, roomType: String?) {
        voiceRoomGroup.switchRoomMode(roomMode, roomType)
    }

    override fun getCurrentSeatList(): List<RoomSeatInfo>? {
        return viewModel.seatData.value
    }

    override fun updateTopList(data: RoomTopListEntity) {
        viewModel.topListData.postValue(data)
    }

    override fun banUserComment(ban: Boolean) {
        viewModel.restrictedData.value?.let {
            it.banComment = ban
            viewModel.restrictedData.postValue(it)
        }
    }

    override fun changeAdmin(add: Boolean) {
        if (add) {
            viewModel.roomRoleData.postValue(RoomConst.ADMIN_USER)
        } else {
            viewModel.roomRoleData.postValue(RoomConst.NORMAL_USER)
        }
    }

    override fun lockRoom(lock: Boolean) {
        viewModel.roomLockData.postValue(lock)
    }

    override fun closeCommon(close: Boolean) {
    }

    override fun onDestroy() {
        super.onDestroy()
        voiceRoomGroup.onDestroy()
    }

    @Subscribe
    fun onEvent(playSeatVoiceAnimEvent: PlaySeatVoiceAnimEvent) {
        if (TextUtils.equals(playSeatVoiceAnimEvent.uid, LoginRouter.getUserId())) {
            if (viewModel.getMySeatId() < 0) {
                //自己并且未在麦有说话 设置为观众
                if (errorMicCount.get() > ERROR_SEAT_COUNT) {
                    errorMicCount.set(0)
                    MediaHelper.INSTANCE.setRole(false)
                    return
                }
                errorMicCount.incrementAndGet()
                return
            }
            //在麦重置状态
            errorMicCount.set(0)
        }
    }
}