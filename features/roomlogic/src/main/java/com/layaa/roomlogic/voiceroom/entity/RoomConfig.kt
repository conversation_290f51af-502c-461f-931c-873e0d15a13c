package com.layaa.roomlogic.voiceroom.entity

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

/**
 * Created by <PERSON> on 25/09/02_周二
 */
@Keep
data class RoomConfig(
    @SerializedName("supportSlotNum")
    val supportMicCount: List<Int> = listOf(5, 9, 12),

    @SerializedName("slotBossMicSwitchMap")
    val bossMicSwitch: Map<Int, Boolean> = emptyMap(),

    val isShowStickerInChat: Boolean = true
)