package com.layaa.roomlogic.viewmodel

import android.os.Bundle
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.layaa.accountapi.login.LoginRouter
import com.layaa.accountapi.profile.ProfileConst
import com.layaa.accountapi.uploadfile.UploadFileRouter
import com.layaa.chatapi.ImEvent
import com.layaa.im.gson.GsonUtils
import com.layaa.language.LanguageController
import com.layaa.libnet.RetrofitFactory
import com.layaa.libnet.util.Failure
import com.layaa.libnet.util.Result
import com.layaa.libnet.util.Success
import com.layaa.libnet.util.handle
import com.layaa.libui.getString
import com.layaa.libutils.kv.KVDelegate
import com.layaa.libutils.module_log.LogUtils
import com.layaa.libutils.toast.ToastUtils
import com.layaa.roomapi.RoomConst
import com.layaa.roomapi.entity.RoomItemEntity
import com.layaa.roomapi.router.RoomRouter
import com.layaa.roomlogic.R
import com.layaa.roomlogic.api.RoomRepository
import com.layaa.roomlogic.constant.ApiConstant
import com.layaa.roomlogic.datastore.BaseRoomDataStore
import com.layaa.roomlogic.entity.RoomMediaEntity
import com.layaa.roomlogic.entity.RoomProfileEntity
import com.layaa.roomlogic.entity.RoomRestrictedInfo
import com.layaa.roomlogic.entity.RoomSeatInfo
import com.layaa.roomlogic.entity.RoomTopListEntity
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.entity.SPEAK_ADMIN
import com.layaa.roomlogic.entity.SPEAK_FOLLOW
import com.layaa.roomlogic.entity.SPEAK_SELF
import com.layaa.roomlogic.entity.SpeakConfigDto
import com.layaa.roomlogic.module.annotations.RoomMode
import com.layaa.roomlogic.module.helper.MediaHelper
import com.layaa.roomlogic.module.helper.RoomImHelper
import com.layaa.roomlogic.rocket.bean.RocketData
import com.layaa.roomlogic.voiceroom.entity.RoomConfig
import com.layaa.widget.mvvm.BaseViewModel
import kotlinx.coroutines.launch
import org.json.JSONObject

/**
 *<AUTHOR>
 *@date 2021/7/16
 *@des
 **/
class RoomInfoViewModel : BaseViewModel() {

    var roomMode = RoomMode.MODE_VOICE
    val roomProfileData = MutableLiveData<RoomProfileEntity>()
    val updateRoomInfoLoading = MutableLiveData<Boolean>()
    val enterRoomData = MutableLiveData<Result<RoomMediaEntity>>()
    val roomQueryData = MutableLiveData<Result<RoomItemEntity>>()
    val roomUserCountData = MutableLiveData<Int>()
    val roomRoleData = MutableLiveData<Int>()
    val roomLockData = MutableLiveData<Boolean>()
    val seatUserData = MutableLiveData<HashMap<String, RoomUserEntity>>()
    val seatData = MutableLiveData<List<RoomSeatInfo>>()
    val restrictedData = MutableLiveData<RoomRestrictedInfo>()
    val relationStatusData = MutableLiveData<Int>()
    val topListData = MutableLiveData<RoomTopListEntity>()
    val giftValueAmount = MutableLiveData<Long>()
    val seatCountData = MutableLiveData<Int>()
    val speakConfigData = MutableLiveData<SpeakConfigDto>()
    val seatCountChangeData = MutableLiveData<Boolean>()
    val roomRocketLiveData = MutableLiveData<RocketData>()

    fun enterLiveRoom(roomId: String?, password: String?) = viewModelScope.launch {
        try {
            val params = HashMap<String, String>()
            roomId?.let {
                params["roomId"] = it
            }
            password?.let {
                params["password"] = it
            }
            val result = RoomRepository.enterLiveRoom(params)
            result.roomName = result.liveRoomProfile?.roomName
            result.notice = result.liveRoomProfile?.notice
            result.photo = result.liveRoomProfile?.coverUrl
            result.roomId = result.liveRoomProfile?.roomId
            enterRoomData.postValue(Success(result))
        } catch (e: Exception) {
            enterRoomData.postValue(Failure(e))
        }
    }

    fun queryRoomInfo(roomId: String?) = viewModelScope.launch {
        try {
            val result = RoomRepository.queryProfileRoom(roomId)
            roomQueryData.postValue(Success(result))
        } catch (e: Exception) {
            roomQueryData.postValue(Failure(e))
        }
    }

    fun asyncLiveRoomInfo(roomId: String?) = viewModelScope.launch {
        try {
            val params = HashMap<String, String>()
            roomId?.let {
                params["roomId"] = roomId
            }
            RoomRepository.syncLiveRoomInfo(params)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    fun quitLive(roomId: String?, ownerId: String?, reason: String?) = viewModelScope.launch {
        try {
            if (TextUtils.equals(LoginRouter.getUserId(), ownerId)) {
                val bundle = Bundle()
                bundle.putString(RoomConst.ROOM_ID, roomId)
                RoomRouter.broadcastEndLive(bundle)
            } else {
                //观众 直接离开
                if (!TextUtils.equals(BaseRoomDataStore.QUIT_ROOM_LIVE_END, reason)) {
                    //非直播关播 才要调用退房接口
                    val params = hashMapOf("roomId" to roomId!!)
                    RoomRepository.liveLeave(params)
                }
            }
        } catch (e: Exception) {
            LogUtils.e(e)
        }
    }

    fun enterRoom(roomId: String?, password: String?) = viewModelScope.launch {
        try {
            val params = HashMap<String, String>()
            roomId?.let {
                params["roomId"] = it
            }
            password?.let {
                params["password"] = it
            }
            val result = RoomRepository.enterRoom(params)
            result.roomPassword = password
            enterRoomData.postValue(Success(result))
        } catch (e: Exception) {
            enterRoomData.postValue(Failure(e, true))
        }
    }

    fun asyncRoomInfo(roomId: String?) = viewModelScope.launch {
        try {
            val params = HashMap<String, String>()
            roomId?.let {
                params["roomId"] = roomId
            }
            RoomRepository.asyncQueryRoomInfo(params)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    fun refreshRoomToken(roomId: String) = viewModelScope.launch {
        try {
            val rtcToken = RoomRepository.refreshRoomToken(roomId)
            MediaHelper.INSTANCE.refreshToken(roomId, rtcToken)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    fun updateRoomInfo(
        roomName: String? = null,
        notice: String? = null,
        photo: String? = null,
        seatPermission: String? = null
    ) = viewModelScope.launch {

        try {
            updateRoomInfoLoading.postValue(true)

            val params = HashMap<String, String>()
            params["roomId"] = roomProfileData.value?.roomId!!
            roomName?.let {
                params.put("roomName", it)
            }
            notice?.let {
                params.put("notice", it)
            }
            photo?.let {
                params.put("photo", it)
            }
            seatPermission?.let {
                params.put("seatPermission", it)
            }
            val result = RoomRepository.updateRoomInfo(params)
            if (!TextUtils.equals(
                    result.notice,
                    roomProfileData.value?.notice
                )
            ) {
                //修改了公告
                RoomImHelper.instance.addTextToMessageList(
                    result.notice,
                    ImEvent.CustomRoomMsg.TYPE_NOTICE,
                )
            }
            roomProfileData.value?.roomName = result.roomName
            roomProfileData.value?.notice = result.notice
            roomProfileData.value?.photo = result.photo
            roomProfileData.value?.seatState = result.seatState
            roomProfileData.value?.let {
                roomProfileData.postValue(it)
            }
        } catch (e: Exception) {
            e.handle()
        } finally {
            updateRoomInfoLoading.postValue(false)
        }
    }

    fun addFollow(source: String = "LIVE_ROOM") = viewModelScope.launch {
        try {
            val params: MutableMap<String, String> = HashMap()
            roomProfileData.value?.ownerId?.let {
                params[ApiConstant.KEY_REMOTE_UID] = it
            } ?: run {
                //todo 先从房间id里截取出来
                params[ApiConstant.KEY_REMOTE_UID] =
                    roomProfileData.value?.roomId?.replace("R-", "", true) ?: ""
            }
            roomProfileData.value?.roomId?.let {
                params["roomId"] = it
            }
            params["source"] = source
            val result = RoomRepository.addFollow(params)
            relationStatusData.postValue(result ?: ProfileConst.TYPE_FOLLOW.toInt())
            ToastUtils.show(getString(R.string.text_followed))
        } catch (e: Exception) {
            RetrofitFactory.handleError(e, true)
        }

    }

    fun cancel(source: String = "LIVE_ROOM") = viewModelScope.launch {
        try {
            val params: MutableMap<String, String> = HashMap()
            roomProfileData.value?.ownerId?.let {
                params[ApiConstant.KEY_REMOTE_UID] = it
            } ?: run {
                //todo 先从房间id里截取出来
                params[ApiConstant.KEY_REMOTE_UID] =
                    roomProfileData.value?.roomId?.replace("R-", "", true) ?: ""
            }
            params["source"] = source
            roomProfileData.value?.roomId?.let {
                params["roomId"] = it
            }
            val result = RoomRepository.cancelFollow(params)
            relationStatusData.postValue(result ?: ProfileConst.TYPE_NO_FRIEND.toInt())
            ToastUtils.show(
                LanguageController.getInstance().getString(R.string.vr_follow_cancel_toast)
            )
        } catch (e: Exception) {
            RetrofitFactory.handleError(e, true)
        }
    }

    fun updateLiveProfile(params: MutableMap<String, String>) = viewModelScope.launch {
        try {

            if (getRoomId()?.startsWith("R-", true) == true) {
                //语音房房
                RoomRepository.updateRoomInfo(params)
            } else {
                //视频房
                RoomRepository.updateLiveProfile(params)
            }
        } catch (e: Exception) {
            RetrofitFactory.handleError(e, true)
        }
    }

    fun switchCommentLevel(level: Int?, role: Int?) = viewModelScope.launch {
        try {
            val params = HashMap<String, String>()
            val json = JSONObject()
            json.put("level", level)
            json.put("userRoleLimit", role)
            params["speakConfigDto"] = json.toString()
            params["roomId"] = getRoomId() ?: ""
            //语音房房
            if (getRoomId()?.startsWith("R-", true) == true) {
                RoomRepository.updateRoomInfo(params)
            } else {
                //视频房
                RoomRepository.updateLiveProfile(params)
            }

            val data = speakConfigData.value ?: SpeakConfigDto()
            if (level != null) {
                data.level = level
            }
            if (role != null) {
                data.userRoleLimit = role
            }
            speakConfigData.postValue(data)
        } catch (e: Exception) {
            RetrofitFactory.handleError(e, true)
        }
    }

    fun switchCommentStatus(enable: Boolean) = viewModelScope.launch {
        try {
            val params = HashMap<String, String>()
            val json = JSONObject()
            json.put("enable", enable)
            params["speakConfigDto"] = json.toString()
            params["roomId"] = getRoomId() ?: ""
            if (getRoomId()?.startsWith("R-", true) == true) {
                //语音房房
                RoomRepository.updateRoomInfo(params)
            } else {
                //视频房
                RoomRepository.updateLiveProfile(params)
            }
            speakConfigData.value?.let {
                it.enable = enable
                speakConfigData.postValue(it)
            } ?: run {
                speakConfigData.postValue(SpeakConfigDto(enable = enable))
            }
        } catch (e: Exception) {
            RetrofitFactory.handleError(e, true)
        }
    }

    fun uploadVoiceImage(photo: String) = viewModelScope.launch {
        try {
            updateRoomInfoLoading.postValue(true)
            val uploadResult = UploadFileRouter.uploadFileToServer(
                photo,
                UploadFileRouter.ResourceType.AVATAR,
            )
            updateRoomInfo(null, null, uploadResult.objectKey, null)
        } catch (e: Exception) {
            e.handle()
        } finally {
            updateRoomInfoLoading.postValue(false)
        }
    }

    fun updateSeatsCount(seatCount: Int, isFromPK: Boolean = false) = viewModelScope.launch {
        try {
            RoomRepository.updateSeatsCount(getRoomId(), seatCount)
            seatCountChangeData.postValue(isFromPK)
        } catch (e: Exception) {
            RetrofitFactory.handleError(e, true)
        }
    }

    fun resetRoomCover() = viewModelScope.launch {
        try {
            getRoomId()?.let { RoomRepository.resetRoomCover(it, "7") }

        } catch (e: Exception) {
            LogUtils.e(e)
        }
    }

    fun setRoomRocketStatus(rocketData: RocketData) = viewModelScope.launch {
        try {
            roomRocketLiveData.postValue(rocketData)
        } catch (e: Exception) {
            LogUtils.e(e)
        }
    }

    fun getRoomId(): String? {
        return roomProfileData.value?.roomId
    }

    fun getMySeatId(): Int {
        return getSeatId(LoginRouter.getUserId())
    }

    fun getSeatId(userId: String?): Int {
        return seatUserData.value?.get(userId)?.seatInfo?.seatId ?: -1
    }

    fun getSeatInfo(userId: String?): RoomSeatInfo? {
        return seatUserData.value?.get(userId)?.seatInfo
    }

    fun isNormalUser(): Boolean {
        return (roomRoleData.value ?: 0) == RoomConst.NORMAL_USER
    }

    fun isRoomOwn(): Boolean {
        return (roomRoleData.value ?: 0) == RoomConst.ROOM_OWNER
    }

    fun isRoomManager(): Boolean {
        return (roomRoleData.value ?: 0) == RoomConst.ADMIN_USER
    }

    fun canSendMsg(): Pair<Boolean, String> {
        if (speakConfigData.value?.enable != true) {
            return Pair(
                false,
                LanguageController.getInstance()
                    .getString(R.string.vr_comment_disable_notice)
            )
        }

        if (isRoomOwn()) {
            return Pair(true, "")
        }

        if (restrictedData.value?.banComment == true) {
            return Pair(
                false,
                LanguageController.getInstance().getString(R.string.vr_profile_comment_disabled)
            )
        }

        var hasLevel = false
        val wealthLevel = speakConfigData.value?.level ?: 1
        if (LoginRouter.getLevel() >= wealthLevel) {
            hasLevel = true
        }

        val userRoleLimit = speakConfigData.value?.userRoleLimit ?: 0
        var hasRole = false

        val tips: String
        when (userRoleLimit) {
            SPEAK_FOLLOW -> {
                val relation = relationStatusData.value ?: 0
                if (relation == ProfileConst.TYPE_FOLLOW.toInt() || relation == ProfileConst.TYPE_FRIEND.toInt()) {
                    hasRole = true
                }
                tips = getString(R.string.vr_comment_follower_required, wealthLevel.toString())
            }

            SPEAK_ADMIN -> {
                if (isRoomManager()) {
                    hasRole = true
                }
                tips = getString(R.string.vr_comment_admin_required, wealthLevel.toString())
            }

            SPEAK_SELF -> {
                tips = getString(R.string.vr_comment_owner_required)
                if (isRoomOwn()) {
                    hasRole = true
                } else {
                    return Pair(
                        false,
                        tips
                    )
                }
            }

            else -> {
                hasRole = true
                tips = getString(R.string.vr_comment_level_required, wealthLevel.toString())
            }
        }

        if (!hasLevel || !hasRole) {
            return Pair(false, tips)
        }
        return Pair(true, "")
    }


}