package com.layaa.roomlogic.api

import com.layaa.libnet.bean.ApiResponseEntity
import com.layaa.libnet.bean.ApiResponseNonDataWareEntity
import com.layaa.roomapi.entity.RoomItemEntity
import com.layaa.roomlogic.constant.ApiConstant
import com.layaa.roomlogic.entity.LiveLinkMicItemEntity
import com.layaa.roomlogic.entity.LivePKConfigEntity
import com.layaa.roomlogic.entity.LiveRankListEntity
import com.layaa.roomlogic.entity.LiveRoomAnchorListEntity
import com.layaa.roomlogic.entity.LockInfoEntity
import com.layaa.roomlogic.entity.PKRecordsListEntity
import com.layaa.roomlogic.entity.PlayCenterList
import com.layaa.roomlogic.entity.QueryFamilyInfoBean
import com.layaa.roomlogic.entity.RelationListEntity
import com.layaa.roomlogic.entity.RoomBannerEntity
import com.layaa.roomlogic.entity.RoomConfigEntity
import com.layaa.roomlogic.entity.RoomListEntity
import com.layaa.roomlogic.entity.RoomManagerRecordBean
import com.layaa.roomlogic.entity.RoomManagerRecordList
import com.layaa.roomlogic.entity.RoomMediaEntity
import com.layaa.roomlogic.entity.RoomOperationRecordBean
import com.layaa.roomlogic.entity.RoomProfileEntity
import com.layaa.roomlogic.entity.RoomUserEntity
import com.layaa.roomlogic.entity.SearchRoomEntity
import com.layaa.roomlogic.entity.SearchRoomUserResult
import com.layaa.roomlogic.entity.SearchUserEntity
import com.layaa.roomlogic.entity.ShareUrlEntity
import com.layaa.roomlogic.entity.VideoRoomItemEntity
import com.layaa.roomlogic.entity.VideoRoomListEntity
import com.layaa.roomlogic.module.score.ScoreBoard
import com.layaa.roomlogic.module.score.ScoreRecordDetail
import com.layaa.roomlogic.module.score.ScoreRecordList
import com.layaa.roomlogic.ranking.date.RankingBean
import com.layaa.roomlogic.rocket.bean.GrabRewardEntity
import com.layaa.roomlogic.rocket.bean.RewardRecordEntity
import com.layaa.roomlogic.rocket.bean.RocketLevelDetailEntity
import com.layaa.roomlogic.share.ShareFriendList
import com.layaa.roomlogic.voiceroom.entity.RoomConfig
import com.layaa.roomlogic.voiceroom.entity.UserOnlineListEntity
import com.layaa.roomlogic.voiceroom.entity.VoiceGiftRecordEntity
import com.layaa.shopapi.bean.ProductOwnListEntity
import com.layaa.shopapi.bean.ProductStoreListEntity
import io.reactivex.Flowable
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST
import javax.annotation.Nullable

/**
 *<AUTHOR>
 *@date 2020/9/19
 *@des
 **/
interface RoomApi {

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/query/host/list")
    @FormUrlEncoded
    suspend fun getHotList(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}${ApiConstant.PATH_PREFIX_V1}/live/room/feed_list")
    @FormUrlEncoded
    suspend fun getVideoList(@FieldMap map: Map<String, String>): ApiResponseEntity<VideoRoomListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/query/recently/list")
    @FormUrlEncoded
    suspend fun getRecentlyList(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/query/follow/list")
    @FormUrlEncoded
    suspend fun getFollowingList(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/user/updateRoomProfile")
    @FormUrlEncoded
    suspend fun updateRoomInfo(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomProfileEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/query/profile")
    @FormUrlEncoded
    suspend fun queryUserRoom(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomItemEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/query/page/info")
    suspend fun queryPageInfo(): ApiResponseEntity<RoomItemEntity>

    @POST("/room/user/queryUserOpeningRoomId")
    suspend fun queryUserOpeningRoomId(): ApiResponseEntity<String?>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/ops/createRoom")
    @FormUrlEncoded
    suspend fun createRoom(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomItemEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/ops/enterRoom")
    @FormUrlEncoded
    suspend fun enterRoom(@FieldMap params: Map<String, String>): ApiResponseEntity<RoomMediaEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/ops/leaveRoom")
    @FormUrlEncoded
    suspend fun leaveRoom(@FieldMap params: Map<String, String?>): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/ops/closeRoom")
    @FormUrlEncoded
    suspend fun closeRoom(@FieldMap params: Map<String, String?>): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/sync/queryFullRoomInfoAsync")
    @FormUrlEncoded
    suspend fun asyncQueryRoomInfo(@FieldMap params: Map<String, String>): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/ops/queryArgoToken")
    @FormUrlEncoded
    suspend fun refreshRoomToken(@Field("roomId") roomId: String): ApiResponseEntity<String>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/selectSeat")
    @FormUrlEncoded
    suspend fun selectSeat(@FieldMap params: Map<String, String>): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/leaveSeat")
    @FormUrlEncoded
    suspend fun leaveSeat(@FieldMap params: Map<String, String>): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/user/updateMicStatus")
    @FormUrlEncoded
    suspend fun updateMicStatus(
        @Field("micStatus") type: Int,
        @Field("roomId") roomId: String,
        @Field("uid") uid: String
    ): ApiResponseNonDataWareEntity

    /**
     * manageType
     * 1 静音 2 解除静音
     * 3 锁定 4 解除锁定
     */
    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/seatManage")
    @FormUrlEncoded
    suspend fun seatManage(
        @Field("manageType") type: Int,
        @Field("roomId") roomId: String,
        @Field("seatId") seatId: Int, @Field("uid") uid: String
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/inviteSeat")
    @FormUrlEncoded
    suspend fun seatInvite(
        @Field("roomId") roomId: String?,
        @Field("beInvitedUid") userId: String?,
        @Nullable @Field("seatId") seatId: Int?
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/acceptSeatInvite")
    @FormUrlEncoded
    suspend fun acceptSeatInvite(
        @Field("roomId") roomId: String?,
        @Field("inviteId") inviteId: String?,
        @Nullable @Field("seatId") seatId: Int?
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/manage/kickRoomUser")
    @FormUrlEncoded
    suspend fun kickRoomUser(
        @Field("roomId") roomId: String,
        @Field("remoteUid") userId: String,
        @Field("uid") uid: String
    ): ApiResponseNonDataWareEntity

    /**
     * 1 增加 2删除
     */
    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/roleManage")
    @FormUrlEncoded
    suspend fun changeRoleManage(
        @Field("roomId") roomId: String?,
        @Field("roomManageInfoDTO") roomManageInfoDTO: String?,
        @Field("opCode") opCode: Int
    ): ApiResponseNonDataWareEntity

    /**
     * @param params uid uid
     *               source ""
     *               skip pageIndex
     *               limit pageCount
     * @return data
     */
    @POST("${ApiConstant.PATH_PREFIX_V1}/user/interact/getFriendList")
    @FormUrlEncoded
    suspend fun getFriendList(@FieldMap params: HashMap<String, String>): ApiResponseEntity<ShareFriendList>

    @POST("/share/getShareUrl")
    @FormUrlEncoded
    suspend fun getShareUrl(
        @Field("roomId") roomId: String
    ): ApiResponseEntity<ShareUrlEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/backend/config/queryBanner")
    @FormUrlEncoded
    fun queryBannerPresenter(@FieldMap params: Map<String, String>): Flowable<ApiResponseEntity<ArrayList<RoomBannerEntity>>>

    @POST("${ApiConstant.PATH_PREFIX_V2}/user/client/search")
    @FormUrlEncoded
    suspend fun searchUser(@Field("content") content: String): ApiResponseEntity<ArrayList<SearchUserEntity>>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/searchOnlineUser")
    @FormUrlEncoded
    suspend fun searchRoomUser(
        @Field("roomId") roomId: String?,
        @Field("searchField") searchField: String,
    ): ApiResponseEntity<SearchRoomUserResult>

    //@POST("/room/query/likeSearch/hot/list")
    @POST("${ApiConstant.PATH_PREFIX_V2}/room/search")
    @FormUrlEncoded
    suspend fun searchRoom(@FieldMap params: Map<String, String>): ApiResponseEntity<ArrayList<SearchRoomEntity>>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/family/searchFamily")
    @FormUrlEncoded
    suspend fun searchFamily(@Field("familyId") familyId: String): ApiResponseEntity<ArrayList<QueryFamilyInfoBean>>//搜索家族

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/family/applyFamily")
    @FormUrlEncoded
    suspend fun applyFamily(
        @Field("familyId") familyId: String
    ): ApiResponseEntity<Boolean>//申请加入家族

    @POST(ApiConstant.ROOM_BANNER)
    @FormUrlEncoded
    suspend fun queryBanner(@FieldMap map: Map<String, String>): ApiResponseEntity<ArrayList<RoomBannerEntity>>

    @POST("${ApiConstant.ROOM_USER_PROFILE_URL}")
    @FormUrlEncoded
    suspend fun getUserProfile(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomUserEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/page_info")
    @FormUrlEncoded
    suspend fun getLivePageInfo(@FieldMap map: Map<String, String>): ApiResponseEntity<VideoRoomItemEntity>

    @POST("live/room/start")
    @FormUrlEncoded
    suspend fun startLive(@FieldMap map: Map<String, String>): ApiResponseEntity<VideoRoomItemEntity>

    @POST("live/room/random_title")
    @FormUrlEncoded
    suspend fun getRandomTitle(
        @Field("lastTitleId") lastTitleId: String?
    ): ApiResponseEntity<String>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/enter")
    @FormUrlEncoded
    suspend fun enterLiveRoom(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomMediaEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/sync_full_room_info")
    @FormUrlEncoded
    suspend fun syncLiveRoomInfo(@FieldMap map: Map<String, String>): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/follow")
    @FormUrlEncoded
    suspend fun followLiveRoom(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/unfollow")
    @FormUrlEncoded
    suspend fun unFollowLiveRoom(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/like")
    @FormUrlEncoded
    suspend fun liveLike(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/rank/queryRank")
    @FormUrlEncoded
    suspend fun getLiveRank(@FieldMap map: Map<String, String>): ApiResponseEntity<LiveRankListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/userManage")
    @FormUrlEncoded
    suspend fun userMange(
        @Field("roomId") roomId: String?,
        @Field("remoteUid") remoteUid: String?,
        @Field("restrictedType") restrictedType: String,
        @Field("action") action: String,
        @Field("durationSeconds") durationSeconds: Int?
    ): ApiResponseEntity<Boolean?>

    @POST("/user/relation/removeBlack")
    @FormUrlEncoded
    suspend fun removeBlack(@Field("remoteUid") remoteUid: String?): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/clear_screen")
    @FormUrlEncoded
    suspend fun liveClearScreen(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/getManagerList")
    @FormUrlEncoded
    suspend fun getRoomManagerList(@FieldMap map: Map<String, String>): ApiResponseEntity<List<RoomUserEntity>>


    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/getManagementRecordList")
    @FormUrlEncoded
    suspend fun getManagementRecordList(
        @Field("roomId") roomId: String?,
        @Field("pageNum") pageNum: Int,
        @Field("pageSize") pageSize: Int
    ): ApiResponseEntity<RoomManagerRecordList<RoomOperationRecordBean>>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/getRestrictedRecordList")
    @FormUrlEncoded
    suspend fun getRestrictedRecordList(
        @Field("roomId") roomId: String?,
        @Field("pageNum") pageNum: Int,
        @Field("pageSize") pageSize: Int,
        @Field("types") types: String
    ): ApiResponseEntity<RoomManagerRecordList<RoomManagerRecordBean>>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/leave")
    @FormUrlEncoded
    suspend fun liveLeave(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/end")
    @FormUrlEncoded
    suspend fun closeLiveRoom(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/update_profile")
    @FormUrlEncoded
    suspend fun updateLiveProfile(@FieldMap map: Map<String, String>): ApiResponseEntity<VideoRoomItemEntity>

    @POST("${ApiConstant.ROOM_USER_ADD_FOLLOW_URL}")
    @FormUrlEncoded
    suspend fun addFollow(@FieldMap params: Map<String, String>): ApiResponseEntity<Int?>

    @POST("${ApiConstant.ROOM_USER_CANCEL_FOLLOW_URL}")
    @FormUrlEncoded
    suspend fun cancelFollow(@FieldMap params: Map<String, String>): ApiResponseEntity<Int?>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/kick")
    @FormUrlEncoded
    suspend fun kickMic(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/anchorApplyList")
    @FormUrlEncoded
    suspend fun getAnchorApplyList(@FieldMap map: Map<String, String>): ApiResponseEntity<LiveRoomAnchorListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/anchorQueryApplyList")
    @FormUrlEncoded
    suspend fun getLinkAudienceApplyList(@FieldMap map: Map<String, String>): ApiResponseEntity<LiveRoomAnchorListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/anchorAcceptOp")
    @FormUrlEncoded
    suspend fun reactLinkAnchor(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/vcOrPkMatchInviteAcceptOp")
    @FormUrlEncoded
    suspend fun reactMatchAnchor(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/anchorProcessApplication")
    @FormUrlEncoded
    suspend fun reactLinkAudience(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/anchorFriendsList")
    @FormUrlEncoded
    suspend fun getContributionList(@FieldMap map: Map<String, String>): ApiResponseEntity<LiveRoomAnchorListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/searchUser")
    @FormUrlEncoded
    suspend fun searchUserById(@FieldMap map: Map<String, String>): ApiResponseEntity<LiveLinkMicItemEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/anchorApplyOp")
    @FormUrlEncoded
    suspend fun linkAnchor(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>


    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/audienceQueryApplyList")
    @FormUrlEncoded
    suspend fun audienceGetLinkMicList(@FieldMap map: Map<String, String>): ApiResponseEntity<LiveRoomAnchorListEntity>


    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/audienceProcessApplication")
    @FormUrlEncoded
    suspend fun audienceDoLinkMic(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomUserEntity>


    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/audienceConfirmInvitation")
    @FormUrlEncoded
    suspend fun audienceReactReceivedLink(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/match")
    @FormUrlEncoded
    suspend fun anchorMatch(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/anchorVerify")
    @FormUrlEncoded
    suspend fun verifyMatch(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/acceptVcTransferPK")
    @FormUrlEncoded
    suspend fun dealLinkToPK(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/anchorProcessInvitation")
    @FormUrlEncoded
    suspend fun anchorProcessInvitation(@FieldMap map: Map<String, String>): ApiResponseEntity<RoomUserEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/anchorSetMuteStatus")
    @FormUrlEncoded
    suspend fun anchorMuteAudience(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/anchorHangUp")
    @FormUrlEncoded
    suspend fun anchorHangUpAudience(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/audience/audienceHangUp")
    @FormUrlEncoded
    suspend fun audienceHangUpAnchor(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/muteAnchor")
    @FormUrlEncoded
    suspend fun anchorMuteAnchor(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/hangUp")
    @FormUrlEncoded
    suspend fun anchorHangUpAnchor(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/pkHangup")
    @FormUrlEncoded
    suspend fun pkHangUp(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/pkCancel")
    @FormUrlEncoded
    suspend fun pkCancelInvite(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/pkApply")
    @FormUrlEncoded
    suspend fun pkInviteUser(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/vcTransferPK")
    @FormUrlEncoded
    suspend fun pkConvert(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/refusePk")
    @FormUrlEncoded
    suspend fun pkRefuse(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/pkAccept")
    @FormUrlEncoded
    suspend fun pkAccept(@FieldMap map: Map<String, String>): ApiResponseEntity<Boolean>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/queryPkRecord")
    @FormUrlEncoded
    suspend fun getPKRecordsList(@FieldMap map: Map<String, String>): ApiResponseEntity<PKRecordsListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/live/room/vc/anchor/queryRegionSwitch")
    @FormUrlEncoded
    suspend fun getPKStatus(@FieldMap map: Map<String, String>): ApiResponseEntity<LivePKConfigEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/ops/queryArgoToken")
    @FormUrlEncoded
    suspend fun queryArgoToken(@FieldMap map: Map<String, String>): ApiResponseEntity<String>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/common/online/user/list")
    @FormUrlEncoded
    suspend fun voiceUserOnlineList(@FieldMap map: Map<String, String>): ApiResponseEntity<UserOnlineListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/common/clearScreenMessage")
    @FormUrlEncoded
    suspend fun clearScreenMessage(@Field("roomId") roomId: String?): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/lock")
    @FormUrlEncoded
    suspend fun lockRoom(
        @Field("roomId") roomId: String?,
        @Field("password") password: String?
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/unlock")
    @FormUrlEncoded
    suspend fun unLockRoom(@Field("roomId") roomId: String?): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/common/permission/getLockInfo")
    @FormUrlEncoded
    suspend fun getLockInfo(@Field("roomId") roomId: String?): ApiResponseEntity<LockInfoEntity?>

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/seat/updateSeatsCount")
    @FormUrlEncoded
    suspend fun updateSeatsCount(
        @Field("roomId") roomId: String?,
        @Field("seatsCount") seatsCount: Int
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V1}/room/common/queryRoomConfig")
    suspend fun queryRoomConfig(): ApiResponseEntity<RoomConfigEntity>

    @POST("/user/relation/getRelationList")
    @FormUrlEncoded
    suspend fun getRelationList(@FieldMap params: Map<String, String>): ApiResponseEntity<RelationListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/rank/queryGlobalRank")
    @FormUrlEncoded
    suspend fun fetchGlobalRanking(
        @Field("uid") uid: String,
        @Field("regionId") regionId: String,
        @Field("roomType") roomType: Int,
        @Field("tab") giftType: String,
        @Field("type") dateType: String,
    ): ApiResponseEntity<RankingBean>


    @POST("${ApiConstant.PATH_PREFIX_V2}/room/play/scoreboard/switch")
    @FormUrlEncoded
    suspend fun scoreBoardSwitch(
        @Field("roomId") roomId: String,
        @Field("voiceChatMappingId") voiceChatMappingId: String,
        @Field("action") action: Int,
        @Field("duration") duration: Int?,
        @Field("boardId") boardId: String?,
    ): ApiResponseEntity<ScoreBoard>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/play/scoreboard/query/record")
    @FormUrlEncoded
    suspend fun fetchScoreRecordList(
        @Field("roomId") roomId: String,
        @Field("voiceChatMappingId") voiceChatMappingId: String,
    ): ApiResponseEntity<ScoreRecordList>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/play/scoreboard/query/rank")
    @FormUrlEncoded
    suspend fun fetchScoreDetail(
        @Field("roomId") roomId: String,
        @Field("voiceChatMappingId") voiceChatMappingId: String,
        @Field("boardId") boardId: String?,
    ): ApiResponseEntity<ScoreRecordDetail>

    @POST("${ApiConstant.PATH_PREFIX_V2}/user/room/queryUserLocatedRoom")
    @FormUrlEncoded
    suspend fun queryUserLocatedRoom(
        @Field("uid") uid: String
    ): ApiResponseEntity<RoomProfileEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/query/user${ApiConstant.PATH_PREFIX_V1}/liveRoom")
    @FormUrlEncoded
    suspend fun queryUserLiveRoom(
        @Field("remoteUid") uid: String
    ): ApiResponseEntity<RoomProfileEntity>

    @POST(ApiConstant.ROOM_OWN_PROP_LIST__URL)
    @FormUrlEncoded
    suspend fun queryOwnPropList(
        @Field("propTypeCode") code: String,
        @Field("dataExpireType") dataExpireType: Int,
        @Field("pageIndex") pageIndex: Int,
        @Field("pageSize") pageSize: Int
    ): ApiResponseEntity<ProductOwnListEntity>

    @POST("/prop/shop/querySellingGoods")
    @FormUrlEncoded
    suspend fun querySellingGoods(
        @Field("propTypeCode") code: String,
        @Field("pageIndex") pageIndex: Int,
        @Field("pageSize") pageSize: Int
    ): ApiResponseEntity<ProductStoreListEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/item/change")
    @FormUrlEncoded
    suspend fun updateRoomPropWearState(
        @Field("itemType") code: String,
        @Field("itemId") propId: String,
        @Field("opType") wearState: Int
    ): ApiResponseNonDataWareEntity

    @POST("/prop/buyProp")
    @FormUrlEncoded
    suspend fun buyProp(
        @Field("receiveUid") receiveUid: String,
        @Field("outOrderId") code: String,
        @Field("productId") productId: String,
        @Field("propId") propId: String,
        @Field("propNum") propNum: Int,
        @Field("propType") propType: String,
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/game/visibility/query")
    suspend fun fetchGameVisibility(): ApiResponseEntity<PlayCenterList>

    @POST(ApiConstant.ROOM_GIFT_RECORDS)
    @FormUrlEncoded
    suspend fun fetchRoomGiftRecords(@Field("roomId") roomId: String?): ApiResponseEntity<List<VoiceGiftRecordEntity>>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/super_manager/reset_room_cover")
    @FormUrlEncoded
    suspend fun resetRoomCover(
        @Field("roomId") roomId: String,
        @Field("reason") reason: String
    ): ApiResponseNonDataWareEntity

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/rocket/detail")
    @FormUrlEncoded
    suspend fun queryRocketDetail(@Field("roomId") roomId: String): ApiResponseEntity<RocketLevelDetailEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/rocket/reward/records")
    @FormUrlEncoded
    suspend fun queryRocketRewardRecords(@Field("roomId") roomId: String,@Field("pageNum") pageNum: Int,@Field("pageSize") pageSize: Int): ApiResponseEntity<RewardRecordEntity>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/rocket/rules")
    suspend fun queryRocketRewardRuler(): ApiResponseEntity<String>

    @POST("${ApiConstant.PATH_PREFIX_V2}/room/rocket/reward/open")
    @FormUrlEncoded
    suspend fun startGrabReward(@Field("roomId") roomId: String,@Field("roundId")roundId: String): ApiResponseEntity<GrabRewardEntity>


    @POST("/v2/api/room/play/pk/open")
    @FormUrlEncoded
    suspend fun openPK(
        @Field("roomId") roomId: String,
        @Field("duration") duration: Int,
        @Field("hostParticipate") hostParticipate: Int,
    ): ApiResponseNonDataWareEntity

    @POST("/v2/api/room/play/pk/close")
    @FormUrlEncoded
    suspend fun closePK(
        @Field("roomId") roomId: String,
    ): ApiResponseNonDataWareEntity

    @POST("/v2/api/room/play/pk/start")
    @FormUrlEncoded
    suspend fun startPK(
        @Field("roomId") roomId: String,
    ): ApiResponseNonDataWareEntity

    @POST("/v2/api/room/play/pk/end")
    @FormUrlEncoded
    suspend fun endPK(
        @Field("id") pkId: Int,
    ): ApiResponseNonDataWareEntity

    @POST("/v2/api/vr/config")
    @FormUrlEncoded
    suspend fun fetchRoomConfig(
        @Field("roomId") roomId: String?,
    ): ApiResponseEntity<RoomConfig>
}
